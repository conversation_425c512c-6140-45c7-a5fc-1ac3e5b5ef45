// qmllint disable unqualified

import QtQuick
import QtQuick.Layouts
import QtQuick.Controls.Basic

Rectangle {
    id: container
    width: parent.width * 0.6
    height: parent.height
    color: "#3e424b"

    signal selectTimestamp(real timestamp)
    signal selectComment(string comment)

    required property var selectedExperiment

    ColumnLayout {
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.bottom: parent.bottom
        anchors.topMargin: 10
        anchors.leftMargin: 10
        anchors.rightMargin: 10
        anchors.bottomMargin: 10
        spacing: 5

        Text {
            Layout.alignment: Qt.AlignCenter
            text: container.selectedExperiment ? "Experiment #" + container.selectedExperiment.expNum : "No experiment selected"
            font.pointSize: 24
            color: "white"
        }

        RowLayout {
            Layout.preferredWidth: parent.width
            spacing: 10

            ColumnLayout {
                Layout.alignment: Qt.AlignCenter

                Text {
                    text: container.selectedExperiment ? "Start Date: " + Qt.formatDateTime(new Date(container.selectedExperiment.startDate * 1000), "MM/dd/yyyy") : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "End Date: " + Qt.formatDateTime(new Date(container.selectedExperiment.endDate * 1000), "MM/dd/yyyy") : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "Atomic Mass: " + container.selectedExperiment.atomicMass : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "Atomic #: " + container.selectedExperiment.atomicNumber : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "Max Energy: " + container.selectedExperiment.maxEnergy : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "Rigidity: " + container.selectedExperiment.rigidity : ""
                    color: "white"
                }
            }

            ColumnLayout {
                Layout.alignment: Qt.AlignCenter

                Text {
                    text: container.selectedExperiment ? "Ion Source: " + container.selectedExperiment.ionSrc : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "Target: " + container.selectedExperiment.target : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "q1: " + container.selectedExperiment.q1 : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "q2: " + container.selectedExperiment.q2 : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "q3: " + container.selectedExperiment.q3 : ""
                    color: "white"
                }

                Text {
                    text: container.selectedExperiment ? "Q1/M: " + container.selectedExperiment.chargeMassRatio : ""
                    color: "white"
                }
            }
        }

        TextField {
            id: commentSearchBar
            Layout.fillWidth: true
            visible: container.selectedExperiment
            background: Rectangle {
                color: "#393939"
                radius: 5
            }
            placeholderText: "Search comments by word or phase (e.g. 'tuned', 'cup readings')"
            placeholderTextColor: "white"
            color: "white"
            Keys.onReturnPressed: {
                commentListModel.filter_by_keyword(commentSearchBar.text);
            }
        }

        ScrollView {
            Layout.preferredWidth: parent.width
            Layout.fillHeight: true
            visible: container.selectedExperiment
            background: Rectangle {
                color: "#393939"
            }
            ScrollBar.vertical.policy: ScrollBar.AlwaysOn

            contentItem: ListView {
                id: commentListView
                clip: true
                boundsBehavior: Flickable.StopAtBounds
                model: commentListModel

                property string searchTerm: ""
                property int selectedIndex: -1

                delegate: ItemDelegate {
                    id: commentListItem
                    width: ListView.view.width

                    required property real timestamp
                    required property string comment

                    required property int index

                    text: Qt.formatDateTime(new Date(timestamp * 1000), "MM/dd/yyyy hh:mm:ss") + " | " + comment

                    onClicked: {
                        commentListView.selectedIndex = index;
                        
                        container.selectTimestamp(commentListItem.timestamp);
                        container.selectComment(commentListItem.comment);

                        beamlinePathModel.filter_by_types(["dipole", "steerer", "quad_singlet_magnetic",
                            "quad_doublet_magnetic", "quad_triplet_magnetic", "quad_doublet_electrostatic",
                            "quad_triplet_electrostatic"])
                        beamlinePathModel.filter_by_timestamp(timestamp);
                    }

                    contentItem: Text {
                        text: commentListItem.text
                        color: "white"
                        font.pixelSize: 14
                        elide: Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignLeft
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.left: parent.left
                        anchors.leftMargin: 16
                        anchors.right: parent.right
                        anchors.rightMargin: 16
                    }

                    background: Rectangle {
                        anchors.fill: parent
                        color: {
                            commentListView.selectedIndex === commentListItem.index ? "#48aaad" : "#393939";
                        }
                    }
                }
            }
        }

        RowLayout {
            visible: container.selectedExperiment

            Text {
                text: "Show: "
                color: "white"
                font.bold: true
                font.pointSize: 14
            }

            RadioButton {
                id: showAllDevicesOption
                text: "Beamline Devices"

                indicator: Rectangle {
                    implicitWidth: 26
                    implicitHeight: 26
                    x: showAllDevicesOption.leftPadding
                    y: parent.height / 2 - height / 2
                    radius: 13
                    border.color: showAllDevicesOption.down ? "#48aaad" : "white"
                    color: "#3e424b"

                    Rectangle {
                        width: 18
                        height: 18
                        x: 4
                        y: 4
                        radius: 9
                        color: showAllDevicesOption.down ? "#48aaad" : "white"
                        visible: showAllDevicesOption.checked
                    }
                }

                contentItem: Text {
                    text: showAllDevicesOption.text
                    font: showAllDevicesOption.font
                    opacity: enabled ? 1.0 : 0.3
                    color: showAllDevicesOption.down ? "#48aaad" : "white"
                    verticalAlignment: Text.AlignVCenter
                    leftPadding: showAllDevicesOption.indicator.width + showAllDevicesOption.spacing
                }

                onClicked: {
                    beamlinePathModel.filter_by_types(["dipole", "steerer", "quad_singlet_magnetic",
                        "quad_doublet_magnetic", "quad_triplet_magnetic", "quad_doublet_electrostatic",
                        "quad_triplet_electrostatic"])
                }
            }

            RadioButton {
                id: showOnlySolenoidsOption
                text: "Solenoids"

                indicator: Rectangle {
                    implicitWidth: 26
                    implicitHeight: 26
                    x: showOnlySolenoidsOption.leftPadding
                    y: parent.height / 2 - height / 2
                    radius: 13
                    border.color: showOnlySolenoidsOption.down ? "#48aaad" : "white"
                    color: "#3e424b"

                    Rectangle {
                        width: 18
                        height: 18
                        x: 4
                        y: 4
                        radius: 9
                        color: showOnlySolenoidsOption.down ? "#48aaad" : "white"
                        visible: showOnlySolenoidsOption.checked
                    }
                }

                contentItem: Text {
                    text: showOnlySolenoidsOption.text
                    font: showOnlySolenoidsOption.font
                    opacity: enabled ? 1.0 : 0.3
                    color: showOnlySolenoidsOption.down ? "#48aaad" : "white"
                    verticalAlignment: Text.AlignVCenter
                    leftPadding: showOnlySolenoidsOption.indicator.width + showOnlySolenoidsOption.spacing
                }

                onClicked: {
                    beamlinePathModel.filter_by_type("solenoid");
                }
            }

            RadioButton {
                id: showOnlyResonatorsOption
                text: "Resonators"

                indicator: Rectangle {
                    implicitWidth: 26
                    implicitHeight: 26
                    x: showOnlyResonatorsOption.leftPadding
                    y: parent.height / 2 - height / 2
                    radius: 13
                    border.color: showOnlyResonatorsOption.down ? "#48aaad" : "white"
                    color: "#3e424b"

                    Rectangle {
                        width: 18
                        height: 18
                        x: 4
                        y: 4
                        radius: 9
                        color: showOnlyResonatorsOption.down ? "#48aaad" : "white"
                        visible: showOnlyResonatorsOption.checked
                    }
                }

                contentItem: Text {
                    text: showOnlyResonatorsOption.text
                    font: showOnlyResonatorsOption.font
                    opacity: enabled ? 1.0 : 0.3
                    color: showOnlyResonatorsOption.down ? "#48aaad" : "white"
                    verticalAlignment: Text.AlignVCenter
                    leftPadding: showOnlyResonatorsOption.indicator.width + showOnlyResonatorsOption.spacing
                }

                onClicked: {
                    //beamlinePathModel.filter_by_type("");
                    beamlinePathModel.filter_by_type("resonator");
                }
            }

            TextField {
                id: beamlinePathSearchBar
                Layout.fillWidth: true
                visible: container.selectedExperiment
                background: Rectangle {
                    color: "#393939"
                    radius: 5
                }
                placeholderText: "Filter by device name (e.g. STP201)"
                placeholderTextColor: "white"
                color: "white"
                Keys.onReturnPressed: beamlinePathModel.filter_by_name(beamlinePathSearchBar.text)
            }
        }
    }
}

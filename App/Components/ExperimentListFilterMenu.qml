import QtQuick
import QtQuick.Layouts
import QtQuick.Controls.Basic

Popup {
    id: popup
    width: parent.width
    height: parent.height
    modal: true
    focus: true
    closePolicy: Popup.NoAutoClose

    background: Rectangle {
        color: "#393939"
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 10

        Text {
            text: qsTr("Experiment Filters")
            font.pointSize: 24
            font.bold: true
            color: "white"
            Layout.alignment: Qt.AlignHCenter
        }

        RowLayout {
            Button {
                id: closeButton
                text: qsTr("✕")
                font.pixelSize: 16
                Layout.preferredWidth: 40
                Layout.preferredHeight: 40
                onClicked: {
                    popup.close();
                }

                background: Rectangle {
                    color: "#4c4c4c"
                }

                contentItem: Text {
                    text: closeButton.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    anchors.fill: parent
                }

                ToolTip.text: qsTr("Close")
            }
        }
    }
}

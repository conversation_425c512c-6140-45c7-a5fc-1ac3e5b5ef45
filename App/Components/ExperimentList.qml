// qmllint disable unqualified

import QtQuick
import QtQuick.Controls.Basic
import QtQuick.Layouts

Rectangle {
    id: container
    width: parent.width * 0.4
    height: parent.height
    color: "#393939"

    signal selectExperiment(var experimentData)

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 10

        TextField {
            id: experimentSearchBar
            Layout.fillWidth: true
            Layout.preferredHeight: implicitHeight
            background: Rectangle {
                color: "#3e424b"
                radius: 5
            }
            placeholderText: "Search by parameter (e.g. ionSrc=ECR1)"
            placeholderTextColor: "white"
            color: "white"
            Keys.onReturnPressed: {
                experimentListModel.clear();

                if (experimentSearchBar.text !== "") {
                    var resultSortingParam = paramsDropdown.getSortingQuery();
                    server.get_experiment_data(experimentSearchBar.text + "&" + resultSortingParam);
                } else {
                    paramsDropdown.fetchSortedExperiments();
                }
            }
        }

        ComboBox {
            id: paramsDropdown
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            model: ["Start Date (Most Recent)", "End Date (Most Recent)", "Atomic Mass Ascending", "Atomic Mass Descending", "Atomic Number Ascending", "Atomic Number Descending", "Ion Source", "Target", "q1", "q2", "q3", "Max Energy Ascending", "Max Energy Descending", "Charge-to-Mass Ratio Ascending", "Charge-to-Mass Ratio Descending", "Rigidity Ascending", "Rigidity Descending"]

            function getSortingQuery() {
                var sortingQuery = "";

                if (currentValue === "Atomic Mass Ascending") {
                    sortingQuery = "orderBy=a";
                } else if (currentValue === "Atomic Mass Descending") {
                    sortingQuery = "orderBy=a&desc=true";
                } else if (currentValue === "Atomic Number Ascending") {
                    sortingQuery = "orderBy=z";
                } else if (currentValue === "Atomic Number Descending") {
                    sortingQuery = "orderBy=z&desc=true";
                } else if (currentValue === "Ion Source") {
                    sortingQuery = "orderBy=ionSrc";
                } else if (currentValue === "Target") {
                    sortingQuery = "orderBy=target";
                } else if (currentValue === "q1") {
                    sortingQuery = "orderBy=q1";
                } else if (currentValue === "q2") {
                    sortingQuery = "orderBy=q2";
                } else if (currentValue === "q3") {
                    sortingQuery = "orderBy=q3";
                } else if (currentValue === "End Date (Most Recent)") {
                    sortingQuery = "orderBy=endDate&desc=true";
                } else if (currentValue === "Start Date (Most Recent)") {
                    sortingQuery = "orderBy=startDate&desc=true";
                } else if (currentValue === "Max Energy Ascending") {
                    sortingQuery = "orderBy=maxEnergy";
                } else if (currentValue === "Max Energy Descending") {
                    sortingQuery = "orderBy=maxEnergy&desc=true";
                } else if (currentValue === "Charge-to-Mass Ratio Ascending") {
                    sortingQuery = "orderBy=chargeMassRatio";
                } else if (currentValue === "Charge-to-Mass Ratio Descending") {
                    sortingQuery = "orderBy=chargeMassRatio&desc=true";
                } else if (currentValue === "Rigidity Ascending") {
                    sortingQuery = "orderBy=rigidity";
                } else if (currentValue === "Rigidity Descending") {
                    sortingQuery = "orderBy=rigidity&desc=true";
                }

                return sortingQuery;
            }

            function fetchSortedExperiments() {
                experimentListModel.clear();

                var sortingQuery = getSortingQuery();
                server.get_experiment_data(sortingQuery);
            }

            delegate: ItemDelegate {
                id: delegate
                required property var model
                required property var index
                width: paramsDropdown.width

                MouseArea {
                    id: hoverArea
                    anchors.fill: parent
                    hoverEnabled: true
                    acceptedButtons: Qt.NoButton
                }

                contentItem: Text {
                    text: delegate.model[paramsDropdown.textRole]
                    color: "white"
                    font: paramsDropdown.font
                    elide: Text.ElideRight
                    verticalAlignment: Text.AlignVCenter
                }

                background: Rectangle {
                    color: hoverArea.containsMouse ? "#3e424b" : "#393939"
                }

                highlighted: paramsDropdown.highlightedIndex == index
            }
            indicator: Canvas {
                id: canvas
                x: paramsDropdown.width - width - paramsDropdown.rightPadding
                y: paramsDropdown.topPadding + (paramsDropdown.availableHeight - height) / 2
                width: 12
                height: 8
                contextType: "2d"

                Connections {
                    target: paramsDropdown
                    function onPressedChanged() {
                        canvas.requestPaint();
                    }
                }

                onPaint: {
                    context.reset();
                    context.moveTo(0, 0);
                    context.lineTo(width, 0);
                    context.lineTo(width / 2, height);
                    context.closePath();
                    context.fillStyle = paramsDropdown.pressed ? "#48aaad" : "white";
                    context.fill();
                }
            }
            onActivated: {
                fetchSortedExperiments();
            }
            contentItem: Text {
                leftPadding: 10
                rightPadding: paramsDropdown.indicator.width + paramsDropdown.spacing

                text: paramsDropdown.displayText
                font: paramsDropdown.font
                color: paramsDropdown.activeFocus ? "#48aaad" : "white"
                verticalAlignment: Text.AlignVCenter
                elide: Text.ElideRight
            }
            background: Rectangle {
                color: "#393939"
                implicitWidth: 120
                implicitHeight: 40
                border.color: paramsDropdown.pressed ? "#48aaad" : "white"
                border.width: paramsDropdown.visualFocus ? 2 : 1
                radius: 2
            }
            popup: Popup {
                y: paramsDropdown.height - 1
                width: paramsDropdown.width
                height: Math.min(contentItem.implicitHeight, paramsDropdown.Window.height - topMargin - bottomMargin)
                implicitHeight: contentItem.implicitHeight
                padding: 1
                background: Rectangle {
                    color: "#393939"
                    implicitWidth: 120
                    implicitHeight: 40
                    border.color: paramsDropdown.pressed ? "#48aaad" : "white"
                    border.width: paramsDropdown.visualFocus ? 2 : 1
                    radius: 2
                }

                contentItem: ListView {
                    clip: true
                    implicitHeight: contentHeight
                    model: paramsDropdown.popup.visible ? paramsDropdown.delegateModel : null
                    currentIndex: paramsDropdown.highlightedIndex

                    ScrollIndicator.vertical: ScrollIndicator {}
                }
            }
        }

        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            background: Rectangle {
                color: "#393939"
            }
            ScrollBar.vertical.policy: ScrollBar.AlwaysOn

            Component.onCompleted: server.get_experiment_data("orderBy=startDate&desc=true")

            contentItem: ListView {
                id: experimentListView
                clip: true
                boundsBehavior: Flickable.StopAtBounds
                model: experimentListModel

                property int selectedIndex: -1

                delegate: ItemDelegate {
                    id: experimentListItem
                    width: ListView.view.width

                    required property string expNum
                    required property real startDate
                    required property real endDate
                    required property var atomicMass
                    required property var atomicNumber
                    required property string ionSrc
                    required property string target
                    required property real q1
                    required property real q2
                    required property real q3
                    required property real maxEnergy
                    required property real chargeMassRatio
                    required property real rigidity

                    required property int index

                    text: {
                        var selection = paramsDropdown.displayText;
                        var infoToShow = "Exp #" + expNum;

                        if (selection === "Start Date (Most Recent)") {
                            infoToShow += " | Start Date: " + Qt.formatDateTime(new Date(startDate * 1000), "MM/dd/yyyy");
                        } else if (selection === "End Date (Most Recent)") {
                            infoToShow += " | End Date: " + Qt.formatDateTime(new Date(endDate * 1000), "MM/dd/yyyy");
                        } else if (selection === "Atomic Mass Ascending" || selection === "Atomic Mass Descending") {
                            infoToShow += " | Atomic Mass: " + atomicMass;
                        } else if (selection === "Atomic Number Ascending" || selection === "Atomic Number Descending") {
                            infoToShow += " | Atomic Number: " + atomicNumber;
                        } else if (selection === "Ion Source") {
                            infoToShow += " | Ion Source: " + ionSrc;
                        } else if (selection === "Target") {
                            infoToShow += " | Target: " + target;
                        } else if (selection === "q1") {
                            infoToShow += " | q1: " + q1;
                        } else if (selection === "q2") {
                            infoToShow += " | q2: " + q2;
                        } else if (selection === "q3") {
                            infoToShow += " | q3: " + q3;
                        } else if (selection === "Max Energy Ascending" || selection === "Max Energy Descending") {
                            infoToShow += " | Max Energy: " + maxEnergy;
                        } else if (selection === "Charge-to-Mass Ratio Ascending" || selection === "Charge-to-Mass Ratio Descending") {
                            infoToShow += " | Charge-to-Mass Ratio: " + chargeMassRatio;
                        } else if (selection === "Rigidity Ascending" || selection === "Rigidity Descending") {
                            infoToShow += " | Rigidity: " + rigidity;
                        }

                        return infoToShow;
                    }

                    onClicked: {
                        experimentListView.selectedIndex = index;
                        container.selectExperiment({
                            expNum: expNum,
                            startDate: startDate,
                            endDate: endDate,
                            atomicMass: atomicMass,
                            atomicNumber: atomicNumber,
                            ionSrc: ionSrc,
                            target: target,
                            q1: q1,
                            q2: q2,
                            q3: q3,
                            maxEnergy: maxEnergy,
                            chargeMassRatio: chargeMassRatio,
                            rigidity: rigidity
                        });
                    }

                    contentItem: Text {
                        text: experimentListItem.text
                        color: "white"
                        font.pixelSize: 14
                        elide: Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignLeft
                        leftPadding: 16
                        rightPadding: 16
                    }

                    background: Rectangle {
                        color: experimentListView.selectedIndex === experimentListItem.index ? "#48aaad" : "#393939"
                    }
                }

                Text {
                    id: noResultsText
                    x: parent.width / 2 - width / 2
                    y: parent.height / 2 - height / 2
                    text: "No results"
                    color: "white"
                    font.pixelSize: 16
                    visible: experimentListModel.count == 0
                }
            }
        }
    }
}

import QtQuick
import QtQuick.Layouts
import QtQuick.Controls.Basic

Popup {
    id: popup
    anchors.centerIn: Overlay.overlay
    width: 0.5 * Screen.width
    height: 0.5 * Screen.height
    modal: true
    focus: true
    closePolicy: Popup.NoAutoClose

    background: Rectangle {
        color: "#393939"
    }

    signal saveComment(string html)

    required property string comment
    required property real timestamp
    property string editedComment

    property bool boldActive: false
    property bool italicActive: false
    property bool underlineActive: false
    property bool highlightActive: false
    property bool numberedListActive: false

    onOpened: {
        print(`timestamp of comment: ${timestamp}`);
        editedComment = comment;
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10

        RowLayout {
            Layout.fillWidth: true

            Item {
                Layout.fillWidth: true
            }

            Button {
                id: closeButton
                text: "✕"
                font.pixelSize: 16
                Layout.preferredWidth: 40
                Layout.preferredHeight: 40
                onClicked: {
                    popup.editedComment = "";
                    popup.close();
                }

                background: Rectangle {
                    color: "#4c4c4c"
                }

                contentItem: Text {
                    text: closeButton.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    anchors.fill: parent
                }

                ToolTip.text: "Close"
            }
        }

        Text {
            text: "Comment Editor"
            font.pixelSize: 24
            font.bold: true
            color: "white"
            Layout.alignment: Qt.AlignHCenter
        }

        RowLayout {
            id: txtFormatMenu
            spacing: 0

            Button {
                id: txtBoldBtn
                checkable: true
                checked: popup.boldActive
                icon.color: "white"
                icon.source: "qrc:/assets/icons/bold.svg"
                Layout.preferredWidth: 40
                Layout.preferredHeight: 40
                background: Rectangle {
                    implicitWidth: 100
                    implicitHeight: 40
                    color: txtBoldBtn.checked ? "#48aaad" : "#343434"
                }
                onClicked: {
                    popup.boldActive = !popup.boldActive;
                    popup.applyFormatting("bold");
                }
                ToolTip.text: "Bold"
            }

            Button {
                id: txtItalicBtn
                checkable: true
                checked: popup.italicActive
                icon.color: "white"
                icon.source: "qrc:/assets/icons/italic.svg"
                Layout.preferredWidth: 40
                Layout.preferredHeight: 40
                background: Rectangle {
                    implicitWidth: 100
                    implicitHeight: 40
                    color: txtItalicBtn.checked ? "#48aaad" : "#343434"
                }
                onClicked: {
                    popup.italicActive = !popup.italicActive;
                    popup.applyFormatting("italic");
                }
                ToolTip.text: "Italic"
            }

            Button {
                id: txtUnderlineBtn
                checkable: true
                checked: popup.underlineActive
                icon.color: "white"
                icon.source: "qrc:/assets/icons/underline.svg"
                Layout.preferredWidth: 40
                Layout.preferredHeight: 40
                background: Rectangle {
                    implicitWidth: 100
                    implicitHeight: 40
                    color: txtUnderlineBtn.checked ? "#48aaad" : "#343434"
                }
                onClicked: {
                    popup.underlineActive = !popup.underlineActive;
                    popup.applyFormatting("underline");
                }
                ToolTip.text: "Underline"
            }

            Button {
                id: txtHighlightBtn
                checkable: true
                checked: popup.highlightActive
                icon.color: "white"
                icon.source: "qrc:/assets/icons/highlight.svg"
                Layout.preferredWidth: 40
                Layout.preferredHeight: 40
                background: Rectangle {
                    implicitWidth: 100
                    implicitHeight: 40
                    color: txtHighlightBtn.checked ? "#48aaad" : "#343434"
                }
                onClicked: {
                    popup.highlightActive = !popup.highlightActive;
                    popup.applyFormatting("highlight");
                }
                ToolTip.text: "Highlight"
            }

            /*Button {
                id: txtListBulletedBtn
                checkable: true
                icon.color: "white"
                icon.source: "qrc:/assets/icons/list_bulleted.svg"
                Layout.preferredWidth: 40
                Layout.preferredHeight: 40
                background: Rectangle {
                    implicitWidth: 100
                    implicitHeight: 40
                    color: txtListBulletedBtn.checked ? "#48aaad" : "#343434"
                }
                onClicked: commentTxt.text += "<ul><li>Item</li></ul>"
                ToolTip.text: "Bulleted List"
            }

            Button {
                id: txtListNumberedBtn
                checkable: true
                checked: popup.numberedListActive
                icon.color: "white"
                icon.source: "qrc:/assets/icons/list_numbered.svg"
                Layout.preferredWidth: 40
                Layout.preferredHeight: 40
                background: Rectangle {
                    implicitWidth: 100
                    implicitHeight: 40
                    color: txtListNumberedBtn.checked ? "#48aaad" : "#343434"
                }
                onClicked: commentTxt.text += "<ol><li>Item</li></ol>"
                ToolTip.text: "Numbered List"
            }*/
        }

        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true

            ScrollBar.vertical.policy: ScrollBar.AlwaysOn

            TextArea {
                id: commentTxt
                textFormat: Text.RichText
                wrapMode: Text.WordWrap
                text: popup.editedComment
                verticalAlignment: Text.AlignTop
                horizontalAlignment: Text.AlignLeft
                font.pixelSize: 14
                color: "black"
                selectByMouse: true

                background: Rectangle {
                    color: "white"
                    border.color: "#cccccc"
                    border.width: 1
                }

                Keys.onPressed: function (event) {
                    if (event.key === Qt.Key_Space) {
                        event.accepted = true;
                        popup.handleTextInput(" ");
                    } else if (event.key === Qt.Key_Return || event.key === Qt.Key_Enter) {
                        popup.handleTextInput("\n");
                    } else if (event.text.length > 0 && !event.modifiers && event.key !== Qt.Key_Backspace && event.key !== Qt.Key_Delete) {
                        event.accepted = true;
                        popup.handleTextInput(event.text);
                    }
                }
            }
        }

        Button {
            id: saveCommentBtn
            Layout.alignment: Qt.AlignCenter
            contentItem: Text {
                text: "Save Comment"
                color: "white"
            }
            background: Rectangle {
                color: saveCommentBtn.checked ? "#48aaad" : "darkseagreen"
            }

            onClicked: {
                popup.saveComment(commentTxt.text);
                popup.close();
            }
        }
    }

    function applyFormatting(formatType) {
        var startPos = commentTxt.selectionStart;
        var endPos = commentTxt.selectionEnd;

        if (startPos !== endPos) {
            var selectedText = commentTxt.selectedText;
            var plainText = stripHtmlTags(selectedText);

            var formattedText = "";
            if (getFormatState(formatType)) {
                formattedText = wrapTextWithFormat(plainText, formatType);
            } else {
                formattedText = plainText;
            }

            commentTxt.remove(startPos, endPos);
            commentTxt.insert(startPos, formattedText);
            commentTxt.select(startPos, startPos + formattedText.length);
        }
    }

    function stripHtmlTags(html) {
        return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ');
    }

    function wrapTextWithFormat(text, formatType) {
        var openTag = "";
        var closeTag = "";

        switch (formatType) {
        case "bold":
            openTag = "<b>";
            closeTag = "</b>";
            break;
        case "italic":
            openTag = "<i>";
            closeTag = "</i>";
            break;
        case "underline":
            openTag = "<u>";
            closeTag = "</u>";
            break;
        case "highlight":
            openTag = "<span style='background-color: yellow'>";
            closeTag = "</span>";
            break;
        }

        return openTag + text + closeTag;
    }

    function getFormatState(formatType) {
        switch (formatType) {
        case "bold":
            return popup.boldActive;
        case "italic":
            return popup.italicActive;
        case "underline":
            return popup.underlineActive;
        case "highlight":
            return popup.highlightActive;
        default:
            return false;
        }
    }

    function handleTextInput(text) {
        var startPos = commentTxt.selectionStart;
        var endPos = commentTxt.selectionEnd;

        if (startPos !== endPos) {
            commentTxt.remove(startPos, endPos);
        }

        insertFormattedText(text);
    }

    function insertFormattedText(text) {
        var cursorPos = commentTxt.cursorPosition;
        var formattedText = text;

        if (text === " ") {
            formattedText = "&nbsp;";
        }

        if (popup.highlightActive) {
            formattedText = "<span style='background-color: yellow'>" + formattedText + "</span>";
        }
        if (popup.underlineActive) {
            formattedText = "<u>" + formattedText + "</u>";
        }
        if (popup.italicActive) {
            formattedText = "<i>" + formattedText + "</i>";
        }
        if (popup.boldActive) {
            formattedText = "<b>" + formattedText + "</b>";
        }

        commentTxt.insert(cursorPos, formattedText);
        commentTxt.cursorPosition = cursorPos + formattedText.length;
    }

    function insertTextAtCursor(textToInsert) {
        var cursorPos = commentTxt.cursorPosition;
        commentTxt.insert(cursorPos, textToInsert);
        commentTxt.cursorPosition = cursorPos + textToInsert.length;
        commentTxt.forceActiveFocus();
    }
}

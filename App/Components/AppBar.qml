import QtQuick
import QtQuick.Controls.Basic

Rectangle {
    width: parent.width
    height: parent.height * 0.1
    color: "#343434"

    signal showDrawer()

    property alias sideBarToggleButton: sideBarToggle

    <PERSON>ton {
        id: sideBarToggle
        anchors.left: parent.left
        anchors.top: parent.top
        anchors.bottom: parent.bottom
        anchors.leftMargin: 10
        anchors.topMargin: 10
        anchors.bottomMargin: 10
        icon.color: "white"
        icon.source: "qrc:/assets/icons/menu.svg"
        width: 50
        height: 50
        background: Rectangle {
            implicitWidth: 100
            implicitHeight: 40
            color: sideBarToggle.down ? "#3e424b" : "#343434"
            radius: 4
        }
        onClicked: parent.showDrawer()
    }

    Text {
        id: titleText
        anchors.centerIn: parent
        text: "ATM"
        font.pointSize: 24
        color: "white"
    }
}

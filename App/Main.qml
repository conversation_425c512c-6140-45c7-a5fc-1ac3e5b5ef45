import QtQuick
import QtQuick.Controls
import Components

ApplicationWindow {
    id: root
    visible: true
    width: Screen.width
    height: Screen.height
    minimumWidth: 0.6 * Screen.width
    minimumHeight: 0.8 * Screen.height
    title: qsTr("ATM")

    menuBar: MenuBar {
        Menu {
            title: qsTr("File")
            Action {
                text: qsTr("Add Comment")
                shortcut: StandardKey.New
                enabled: root.selectedExperiment ?? false
            }
            Action {
                text: qsTr("Edit Comment")
                shortcut: StandardKey.Open
                onTriggered: commentEditor.open()
                enabled: root.selectedComment ?? false
            }
            MenuSeparator {
                width: parent.width
            }
            Action {
                text: qsTr("Close Window")
                shortcut: StandardKey.Close
                onTriggered: root.close()
            }
        }
        Menu {
            title: qsTr("Preload")
            Action {
                text: qsTr("Select Tune")
            }
        }
        Menu {
            title: qsTr("Tunes")
            Action {
                text: qsTr("My Favorites")
            }
            Action {
                text: qsTr("Add Favorite")
            }
        }
        Menu {
            title: qsTr("Profile")
            MenuItem {
                text: qsTr("Show Profile Here...")
            }
        }
        Menu {
            title: qsTr("Tools")
            Action {
                text: qsTr("Experiment Summarizer")
            }
        }
        Menu {
            title: qsTr("Help")
            Action {
                text: qsTr("Open README")
            }
        }
    }

    property bool experimentListVisible: true
    property var selectedExperiment: null
    property var selectedComment: null
    property real selectedTimestamp: 0

    Component.onCompleted: {
        server.experimentCount.connect(function (expCount) {
            experimentListModel.set_exp_count(expCount);
        });

        server.experimentFetched.connect(function (jsonStr) {
            experimentListModel.populate_experiment_list(jsonStr);
        });

        websocketClient.commentFetched.connect(function (jsonStr) {
            commentListModel.populate_comment_list(jsonStr);
        });

        websocketClient.beamlinePathFetched.connect(function (jsonStr) {
            beamlinePathModel.populate_beamline_path(jsonStr);
        });
    }

    AppBar {
        id: appBar
        onShowDrawer: root.experimentListVisible = !root.experimentListVisible
    }

    ExperimentList {
        id: experimentList
        anchors.top: appBar.bottom
        anchors.bottom: parent.bottom
        visible: root.experimentListVisible
        onSelectExperiment: experimentData => root.selectedExperiment = experimentData
    }

    ExperimentViewer {
        id: experimentViewer
        anchors.top: appBar.bottom
        anchors.left: root.experimentListVisible ? experimentList.right : parent.left
        anchors.right: parent.right
        height: parent.height * 0.7
        selectedExperiment: root.selectedExperiment
        onSelectComment: comment => root.selectedComment = comment
        onSelectTimestamp: timestamp => root.selectedTimestamp = timestamp

        Connections {
            target: root

            function onSelectedExperimentChanged() {
                websocketClient.select_exp_num(root.selectedExperiment.expNum);
            }
        }
    }

    BeamlinePath {
        id: beamlinePath
        anchors.top: experimentViewer.bottom
        anchors.bottom: parent.bottom
        anchors.left: experimentViewer.left
        anchors.right: experimentViewer.right
        width: root.width
        height: parent.height * 0.3
        selectedExperiment: root.selectedExperiment
    }

    CommentEditor {
        id: commentEditor
        comment: root.selectedComment
        timestamp: root.selectedTimestamp
        onSaveComment: comment => server.post_comment(root.selectedExperiment.expNum, timestamp, comment)
    }
}
